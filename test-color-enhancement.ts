/**
 * Manual test script to verify color prompt enhancement functionality
 * Run with: npx tsx test-color-enhancement.ts
 */

import { 
  createColorComplianceSection,
  createColorNegativePrompts,
  createColorValidationReminder,
  createComprehensiveColorEnforcement,
  enhancePromptWithColorCompliance,
  createColorExamples,
  validateColorEnforcement
} from './src/utils/colorPromptEnhancer';

import { generateIconPrompt } from './src/utils/promptGenerator';
import { ScrapedWebsiteData } from './src/types';

// Test data
const testColors = ['#3b82f6', '#10b981', '#f59e0b'];
const testWebsiteData: ScrapedWebsiteData = {
  title: 'Test Website',
  description: 'A test website for color enhancement',
  siteName: 'TestSite',
  primaryColors: testColors,
  contentType: 'saas',
  visualStyle: 'modern',
  url: 'https://test.com',
  keywords: ['test', 'website', 'colors'],
  category: 'saas'
};

console.log('🧪 Testing Color Prompt Enhancement Functions\n');

// Test 1: Color Compliance Section
console.log('1. Testing createColorComplianceSection:');
const complianceSection = createColorComplianceSection(testColors);
console.log(complianceSection);
console.log('\n' + '='.repeat(80) + '\n');

// Test 2: Negative Prompts
console.log('2. Testing createColorNegativePrompts:');
const negativePrompts = createColorNegativePrompts(testColors);
console.log(negativePrompts);
console.log('\n' + '='.repeat(80) + '\n');

// Test 3: Color Validation Reminder
console.log('3. Testing createColorValidationReminder:');
const validationReminder = createColorValidationReminder(testColors);
console.log(validationReminder);
console.log('\n' + '='.repeat(80) + '\n');

// Test 4: Comprehensive Color Enforcement
console.log('4. Testing createComprehensiveColorEnforcement:');
const comprehensiveEnforcement = createComprehensiveColorEnforcement(testColors);
console.log(comprehensiveEnforcement);
console.log('\n' + '='.repeat(80) + '\n');

// Test 5: Color Examples
console.log('5. Testing createColorExamples:');
const colorExamples = createColorExamples(testColors);
console.log(colorExamples);
console.log('\n' + '='.repeat(80) + '\n');

// Test 6: Enhanced Prompt Generation
console.log('6. Testing generateIconPrompt with color enhancement:');
const enhancedPrompt = generateIconPrompt(testWebsiteData);
console.log(enhancedPrompt);
console.log('\n' + '='.repeat(80) + '\n');

// Test 7: Prompt Enhancement
console.log('7. Testing enhancePromptWithColorCompliance:');
const originalPrompt = 'Create a modern app icon with clean design';
const enhancedWithColors = enhancePromptWithColorCompliance(originalPrompt, testColors);
console.log('Original:', originalPrompt);
console.log('Enhanced:', enhancedWithColors);
console.log('\n' + '='.repeat(80) + '\n');

// Test 8: Validation
console.log('8. Testing validateColorEnforcement:');
const validationResult = validateColorEnforcement(enhancedPrompt, testColors);
console.log('Validation Result:', JSON.stringify(validationResult, null, 2));
console.log('\n' + '='.repeat(80) + '\n');

// Test 9: Edge Cases
console.log('9. Testing edge cases:');
console.log('Empty colors array:');
console.log(createColorComplianceSection([]));
console.log('Single color:');
console.log(createColorComplianceSection(['#ff0000']));

console.log('\n✅ All tests completed!');
console.log('\n📋 Summary:');
console.log('- Color compliance sections are being generated');
console.log('- Negative prompts are preventing unwanted colors');
console.log('- Validation reminders are enforcing strict compliance');
console.log('- Comprehensive enforcement combines all features');
console.log('- Edge cases are handled gracefully');

// Test 10: Check for key enforcement phrases
console.log('\n🔍 Checking for key enforcement phrases in generated prompt:');
const keyPhrases = [
  'MUST USE ONLY',
  'FORBIDDEN',
  'EXACT HEX',
  'MANDATORY',
  'NEGATIVE PROMPT',
  'COLOR VALIDATION'
];

keyPhrases.forEach(phrase => {
  const found = enhancedPrompt.includes(phrase);
  console.log(`${found ? '✅' : '❌'} "${phrase}": ${found ? 'Found' : 'Missing'}`);
});

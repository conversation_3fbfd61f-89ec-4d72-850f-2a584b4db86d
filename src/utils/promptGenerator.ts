import { ScrapedWebsiteData } from '@/types';
import { createComprehensiveColorEnforcement, createColorExamples } from './colorPromptEnhancer';

/**
 * Maps content categories to icon style descriptions
 */
const CATEGORY_STYLES = {
  ecommerce: {
    elements: ['shopping bag', 'cart', 'storefront', 'package', 'credit card'],
    style: 'commercial and trustworthy',
    mood: 'professional yet approachable'
  },
  blog: {
    elements: ['pen', 'notebook', 'speech bubble', 'document', 'quill'],
    style: 'editorial and readable',
    mood: 'informative and engaging'
  },
  portfolio: {
    elements: ['easel', 'brush', 'camera', 'frame', 'gallery'],
    style: 'creative and artistic',
    mood: 'inspiring and professional'
  },
  saas: {
    elements: ['cloud', 'dashboard', 'graph', 'network', 'gear'],
    style: 'tech-forward and modern',
    mood: 'innovative and reliable'
  },
  corporate: {
    elements: ['building', 'handshake', 'briefcase', 'chart', 'globe'],
    style: 'professional and established',
    mood: 'trustworthy and authoritative'
  },
  creative: {
    elements: ['palette', 'lightbulb', 'star', 'magic wand', 'rainbow'],
    style: 'artistic and expressive',
    mood: 'imaginative and vibrant'
  },
  educational: {
    elements: ['book', 'graduation cap', 'apple', 'chalkboard', 'lightbulb'],
    style: 'educational and clear',
    mood: 'knowledgeable and approachable'
  },
  other: {
    elements: ['circle', 'square', 'diamond', 'star', 'hexagon'],
    style: 'versatile and clean',
    mood: 'neutral and professional'
  }
};

/**
 * Maps visual styles to design descriptions
 */
const VISUAL_STYLE_DESCRIPTIONS = {
  modern: 'sleek, contemporary design with clean lines and subtle gradients',
  minimalist: 'ultra-clean, simple design with plenty of white space and minimal elements',
  corporate: 'professional, business-like appearance with structured layout and conservative colors',
  playful: 'fun, energetic design with rounded corners and vibrant, cheerful elements',
  elegant: 'sophisticated, refined design with premium feel and subtle luxury touches',
  bold: 'strong, impactful design with high contrast and dynamic visual elements',
  classic: 'timeless, traditional design with balanced proportions and established conventions'
};



/**
 * Generates a comprehensive, detailed prompt for icon generation based on website data
 */
export function generateIconPrompt(websiteData: ScrapedWebsiteData): string {
  const category = websiteData.category || 'other';
  const visualStyle = websiteData.visualStyle || 'modern';
  const categoryInfo = CATEGORY_STYLES[category as keyof typeof CATEGORY_STYLES];
  const styleDescription = VISUAL_STYLE_DESCRIPTIONS[visualStyle as keyof typeof VISUAL_STYLE_DESCRIPTIONS];

  // Build comprehensive prompt components
  const components = [];

  // 1. Detailed site identification and branding
  const siteName = websiteData.siteName || websiteData.title || 'website';
  const siteNameInfo = websiteData.siteName && websiteData.title && websiteData.siteName !== websiteData.title
    ? `"${websiteData.siteName}" (${websiteData.title})`
    : `"${siteName}"`;

  components.push(`Create a professional app icon for ${siteNameInfo}`);

  // 2. Complete website description and context
  if (websiteData.description) {
    components.push(`Website description: "${websiteData.description}"`);
  }

  // 3. AI Analysis insights (if available)
  if (websiteData.aiAnalysis) {
    components.push(`AI Analysis - Primary Purpose: ${websiteData.aiAnalysis.primaryPurpose}`);
    components.push(`Brand Personality: ${websiteData.aiAnalysis.brandPersonality}`);
    components.push(`Target Audience: ${websiteData.aiAnalysis.targetAudience}`);
    components.push(`Industry Context: ${websiteData.aiAnalysis.industryContext}`);
    if (websiteData.aiAnalysis.keyFeatures.length > 0) {
      components.push(`Key Features: ${websiteData.aiAnalysis.keyFeatures.join(', ')}`);
    }
    if (websiteData.aiAnalysis.designCharacteristics.length > 0) {
      components.push(`Design Characteristics: ${websiteData.aiAnalysis.designCharacteristics.join(', ')}`);
    }
  }

  // 4. Comprehensive keyword context
  if (websiteData.keywords && websiteData.keywords.length > 0) {
    components.push(`Key website themes and topics: ${websiteData.keywords.join(', ')}`);
  }

  // 4. Category-specific design elements with multiple options
  const allElements = categoryInfo.elements;
  if (allElements.length > 1) {
    components.push(`Incorporate design elements that represent ${category} websites, such as: ${allElements.join(', ')}`);
  } else {
    components.push(`incorporating a ${allElements[0]} as a key design element`);
  }

  // 5. Detailed visual style specification
  components.push(`Design the icon in a ${visualStyle} style characterized by: ${styleDescription}`);
  components.push(`The overall aesthetic should be ${categoryInfo.style} and ${categoryInfo.mood}`);

  // 6. MANDATORY COLOR COMPLIANCE - STRICT REQUIREMENTS
  if (websiteData.primaryColors && websiteData.primaryColors.length > 0) {
    const allColors = websiteData.primaryColors;

    // Use comprehensive color enforcement utility
    const colorEnforcement = createComprehensiveColorEnforcement(allColors);
    components.push(colorEnforcement);

    // Add color usage examples
    const colorExamples = createColorExamples(allColors);
    components.push(colorExamples);
  } else {
    // Enhanced default color suggestions
    const defaultColors = {
      ecommerce: 'professional blue (#2563eb) and trust-building green (#059669) tones',
      blog: 'engaging orange (#ea580c) and readable blue (#2563eb) combination',
      portfolio: 'creative purple (#7c3aed) and modern teal (#0891b2) palette',
      saas: 'tech-forward blue (#1d4ed8) and sophisticated gray (#374151) scheme',
      corporate: 'authoritative navy (#1e3a8a) and premium silver (#6b7280) colors',
      creative: 'vibrant multi-color palette with artistic flair',
      educational: 'approachable blue (#2563eb) and optimistic yellow (#ca8a04) tones',
      other: 'balanced blue (#3b82f6) and neutral gray (#6b7280) combination'
    };
    components.push(`Color scheme: ${defaultColors[category as keyof typeof defaultColors]}`);
  }

  // 7. Technical and design specifications
  components.push(`Format: Square app icon optimized for app stores (iOS App Store, Google Play Store)`);
  components.push(`Design requirements: Clean, scalable vector-style design that remains clear and recognizable at small sizes (16x16px to 1024x1024px)`);
  components.push(`Visual hierarchy: Strong focal point with balanced composition and appropriate negative space`);

  // 9. Brand and identity considerations
  if (websiteData.url) {
    const domain = websiteData.url.replace(/^https?:\/\//, '').replace(/^www\./, '').split('/')[0];
    components.push(`Domain context: ${domain} - ensure the icon reflects this web presence`);
  }

  // 9. Category-specific mood and personality
  components.push(`Emotional tone: ${categoryInfo.mood} with ${categoryInfo.style} characteristics`);

  // 10. Additional visual context from website analysis
  const contextualElements = [];
  if (websiteData.category) {
    contextualElements.push(`${websiteData.category} industry standards`);
  }
  if (websiteData.visualStyle) {
    contextualElements.push(`${websiteData.visualStyle} design principles`);
  }
  if (contextualElements.length > 0) {
    components.push(`Design should align with: ${contextualElements.join(' and ')}`);
  }

  // 11. Final quality and uniqueness requirements
  components.push(`The icon must be distinctive, memorable, and instantly recognizable`);
  components.push(`Avoid generic symbols - create something unique that captures the essence of this specific website`);
  components.push(`Ensure professional quality suitable for official app store submission and brand representation`);

  return components.join('. ') + '.';
}

/**
 * Creates a structured prompt for the AI to generate an even better icon prompt
 */
export function createPromptEnhancementRequest(websiteData: ScrapedWebsiteData): string {
  const basicPrompt = generateIconPrompt(websiteData);

  return `Based on the following comprehensive website analysis, create an enhanced, detailed prompt for generating a perfect app icon:

Complete Website Information:
- Site Name: ${websiteData.siteName || 'Not specified'}
- Page Title: ${websiteData.title || 'Not specified'}
- URL: ${websiteData.url}
- Category: ${websiteData.category || 'Not categorized'}
- Visual Style: ${websiteData.visualStyle || 'Not specified'}
- Full Description: ${websiteData.description || 'No description available'}
- All Keywords: ${websiteData.keywords?.join(', ') || 'No keywords detected'}
- Complete Color Palette: ${websiteData.primaryColors?.join(', ') || 'No colors detected'}
- Favicon URL: ${websiteData.favicon || 'Not available'}
- Open Graph Image: ${websiteData.ogImage || 'Not available'}

Current Generated Prompt: ${basicPrompt}

Please enhance this prompt to create an even more detailed, creative, and specific description for an AI image generator. Focus on:
1. Leveraging ALL the website information provided above (don't truncate or summarize)
2. CRITICAL: Using the EXACT hex color values provided with STRICT ENFORCEMENT LANGUAGE (include phrases like "MUST USE ONLY", "FORBIDDEN", "EXACT HEX VALUES REQUIRED")
3. Adding NEGATIVE PROMPTS to prevent unwanted colors
4. Incorporating insights from the keywords and full description
5. Professional app icon design principles
6. Unique characteristics that make it memorable and brand-appropriate
7. Technical requirements for app store submission
8. Visual elements that specifically represent this website's unique purpose and identity
9. COLOR VALIDATION requirements that treat hex values as non-negotiable

IMPORTANT:
- Use ALL hex color values exactly as specified without any color interpretation
- Include comprehensive context from the full description and keywords
- Don't limit or truncate any of the provided website information
- Create a rich, detailed prompt that takes advantage of modern AI model capabilities

Enhanced Prompt:`;
}

/**
 * Validates and cleans generated prompts
 */
export function validatePrompt(prompt: string): { isValid: boolean; cleanedPrompt?: string; issues?: string[] } {
  const issues: string[] = [];
  let cleanedPrompt = prompt.trim();

  // Check minimum length
  if (cleanedPrompt.length < 20) {
    issues.push('Prompt is too short');
  }

  // Check maximum length (modern AI models can handle much longer prompts)
  if (cleanedPrompt.length > 50000) {
    issues.push('Prompt is extremely long and may hit token limits');
    cleanedPrompt = cleanedPrompt.slice(0, 50000) + '...';
  }

  // Remove potentially problematic content
  const problematicTerms = ['nsfw', 'explicit', 'violent', 'inappropriate'];
  const lowerPrompt = cleanedPrompt.toLowerCase();
  
  for (const term of problematicTerms) {
    if (lowerPrompt.includes(term)) {
      issues.push(`Contains potentially inappropriate content: ${term}`);
    }
  }

  // Ensure it mentions "icon" or "app icon"
  if (!lowerPrompt.includes('icon') && !lowerPrompt.includes('logo')) {
    cleanedPrompt = 'App icon: ' + cleanedPrompt;
  }

  return {
    isValid: issues.length === 0,
    cleanedPrompt,
    issues: issues.length > 0 ? issues : undefined
  };
}

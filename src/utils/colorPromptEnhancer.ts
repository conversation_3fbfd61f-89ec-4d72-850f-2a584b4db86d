/**
 * Utility functions for enhancing prompts with strict color compliance
 */

/**
 * Creates a strict color compliance section for AI image generation prompts
 */
export function createColorComplianceSection(colors: string[]): string {
  if (!colors || colors.length === 0) {
    return '';
  }

  const sections = [];
  
  // Header with emphasis
  sections.push('🎨 MANDATORY COLOR REQUIREMENTS - NON-NEGOTIABLE:');
  
  // Primary color requirement
  sections.push(`PRIMARY BRAND COLOR (REQUIRED): ${colors[0]} - This exact hex color MUST be the dominant color in the icon`);
  
  // Secondary colors if available
  if (colors.length > 1) {
    sections.push(`SECONDARY BRAND COLORS (REQUIRED): ${colors.slice(1).join(', ')} - These exact hex colors MUST be used as accent or supporting colors`);
  }
  
  // Absolute compliance rules
  sections.push(`COLOR COMPLIANCE RULES: You MUST use ONLY these exact hex color values: ${colors.join(', ')}`);
  sections.push(`FORBIDDEN: Do NOT use any colors other than the specified hex values above`);
  sections.push(`REQUIRED: The icon MUST prominently feature ${colors[0]} as the primary color`);
  
  return sections.join('. ') + '.';
}

/**
 * Creates negative prompts to prevent unwanted colors
 */
export function createColorNegativePrompts(allowedColors: string[]): string {
  if (!allowedColors || allowedColors.length === 0) {
    return '';
  }

  const commonUnwantedColors = [
    '#ff0000', '#00ff00', '#0000ff', '#ffff00', 
    '#ff00ff', '#00ffff', '#ffa500', '#800080',
    '#ffc0cb', '#a52a2a', '#808080', '#000000'
  ];
  
  // Filter out any colors that are actually allowed
  const forbiddenColors = commonUnwantedColors.filter(color => 
    !allowedColors.some(allowed => allowed.toLowerCase() === color.toLowerCase())
  );
  
  const negativePrompts = [];
  
  negativePrompts.push(`NEGATIVE PROMPT - FORBIDDEN COLORS: Do NOT use these colors: ${forbiddenColors.slice(0, 6).join(', ')}`);
  negativePrompts.push(`NEGATIVE PROMPT - NO COLOR VARIATIONS: Do NOT use lighter, darker, or modified versions of any colors`);
  negativePrompts.push(`NEGATIVE PROMPT - NO DEFAULT COLORS: Do NOT default to common icon colors unless they exactly match the specified hex values`);
  negativePrompts.push(`NEGATIVE PROMPT - NO GRADIENTS: Do NOT create gradients or color blends that introduce unauthorized colors`);
  
  return negativePrompts.join('. ') + '.';
}

/**
 * Creates a final color validation reminder
 */
export function createColorValidationReminder(colors: string[]): string {
  if (!colors || colors.length === 0) {
    return '';
  }

  return `🔴 FINAL COLOR VALIDATION: This icon MUST use ONLY these exact colors: ${colors.join(', ')} - NO EXCEPTIONS, NO SUBSTITUTIONS, NO VARIATIONS. Every color in the final icon must match exactly one of these hex values.`;
}

/**
 * Creates a comprehensive color enforcement prompt section
 */
export function createComprehensiveColorEnforcement(colors: string[]): string {
  if (!colors || colors.length === 0) {
    return '';
  }

  const sections = [
    createColorComplianceSection(colors),
    createColorNegativePrompts(colors),
    createColorValidationReminder(colors)
  ];

  return sections.filter(section => section.length > 0).join(' ');
}

/**
 * Enhances any existing prompt with strict color compliance
 */
export function enhancePromptWithColorCompliance(originalPrompt: string, colors: string[]): string {
  if (!colors || colors.length === 0) {
    return originalPrompt;
  }

  const colorEnforcement = createComprehensiveColorEnforcement(colors);
  
  // Insert color requirements at the beginning for maximum impact
  return `${colorEnforcement} ${originalPrompt}`;
}

/**
 * Creates color-specific examples for better AI understanding
 */
export function createColorExamples(colors: string[]): string {
  if (!colors || colors.length === 0) {
    return '';
  }

  const examples = [];
  
  if (colors.length >= 1) {
    examples.push(`Example: Use ${colors[0]} for the main icon background or primary element`);
  }
  
  if (colors.length >= 2) {
    examples.push(`Example: Use ${colors[1]} for accent details, borders, or secondary elements`);
  }
  
  if (colors.length >= 3) {
    examples.push(`Example: Use ${colors[2]} for small highlights or tertiary design elements`);
  }
  
  examples.push(`Counter-example: Do NOT use colors like #ff0000 (red), #00ff00 (green), or #0000ff (blue) unless they exactly match the specified hex values`);
  
  return `COLOR USAGE EXAMPLES: ${examples.join('. ')}.`;
}

/**
 * Validates that a prompt contains proper color enforcement language
 */
export function validateColorEnforcement(prompt: string, expectedColors: string[]): {
  isValid: boolean;
  issues: string[];
  suggestions: string[];
} {
  const issues: string[] = [];
  const suggestions: string[] = [];
  const lowerPrompt = prompt.toLowerCase();

  // Check for color enforcement keywords
  const enforcementKeywords = ['must use only', 'forbidden', 'exact hex', 'mandatory', 'required'];
  const hasEnforcement = enforcementKeywords.some(keyword => lowerPrompt.includes(keyword));
  
  if (!hasEnforcement) {
    issues.push('Prompt lacks strong color enforcement language');
    suggestions.push('Add phrases like "MUST USE ONLY", "FORBIDDEN", or "EXACT HEX VALUES REQUIRED"');
  }

  // Check if expected colors are mentioned
  const missingColors = expectedColors.filter(color => 
    !prompt.includes(color) && !prompt.includes(color.toLowerCase())
  );
  
  if (missingColors.length > 0) {
    issues.push(`Missing color specifications: ${missingColors.join(', ')}`);
    suggestions.push('Include all specified hex color values in the prompt');
  }

  // Check for negative prompts
  if (!lowerPrompt.includes('negative prompt') && !lowerPrompt.includes('do not use')) {
    issues.push('Prompt lacks negative prompts to prevent unwanted colors');
    suggestions.push('Add negative prompts that explicitly forbid other colors');
  }

  return {
    isValid: issues.length === 0,
    issues,
    suggestions
  };
}

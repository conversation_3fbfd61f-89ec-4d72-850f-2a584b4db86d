"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analyze-url-for-icon/route";
exports.ids = ["app/api/analyze-url-for-icon/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "node:sqlite":
/*!******************************!*\
  !*** external "node:sqlite" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:sqlite");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("string_decoder");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:dns":
/*!***************************!*\
  !*** external "node:dns" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:dns");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-url-for-icon%2Froute&page=%2Fapi%2Fanalyze-url-for-icon%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-url-for-icon%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-url-for-icon%2Froute&page=%2Fapi%2Fanalyze-url-for-icon%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-url-for-icon%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_pienaaranker_src_personal_icon_generator_src_app_api_analyze_url_for_icon_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/analyze-url-for-icon/route.ts */ \"(rsc)/./src/app/api/analyze-url-for-icon/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/analyze-url-for-icon/route\",\n        pathname: \"/api/analyze-url-for-icon\",\n        filename: \"route\",\n        bundlePath: \"app/api/analyze-url-for-icon/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/src/personal/icon-generator/src/app/api/analyze-url-for-icon/route.ts\",\n    nextConfigOutput,\n    userland: _Users_pienaaranker_src_personal_icon_generator_src_app_api_analyze_url_for_icon_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/analyze-url-for-icon/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZhbmFseXplLXVybC1mb3ItaWNvbiUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGYW5hbHl6ZS11cmwtZm9yLWljb24lMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZhbmFseXplLXVybC1mb3ItaWNvbiUyRnJvdXRlLnRzJmFwcERpcj0lMkZVc2VycyUyRnBpZW5hYXJhbmtlciUyRnNyYyUyRnBlcnNvbmFsJTJGaWNvbi1nZW5lcmF0b3IlMkZzcmMlMkZhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPSUyRlVzZXJzJTJGcGllbmFhcmFua2VyJTJGc3JjJTJGcGVyc29uYWwlMkZpY29uLWdlbmVyYXRvciZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQXNHO0FBQ3ZDO0FBQ2M7QUFDeUM7QUFDdEg7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGdIQUFtQjtBQUMzQztBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxpRUFBaUU7QUFDekU7QUFDQTtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUN1SDs7QUFFdkgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pY29uLWdlbmVyYXRvci8/NmEyZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCIvVXNlcnMvcGllbmFhcmFua2VyL3NyYy9wZXJzb25hbC9pY29uLWdlbmVyYXRvci9zcmMvYXBwL2FwaS9hbmFseXplLXVybC1mb3ItaWNvbi9yb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvYW5hbHl6ZS11cmwtZm9yLWljb24vcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9hbmFseXplLXVybC1mb3ItaWNvblwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvYW5hbHl6ZS11cmwtZm9yLWljb24vcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCIvVXNlcnMvcGllbmFhcmFua2VyL3NyYy9wZXJzb25hbC9pY29uLWdlbmVyYXRvci9zcmMvYXBwL2FwaS9hbmFseXplLXVybC1mb3ItaWNvbi9yb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9hcGkvYW5hbHl6ZS11cmwtZm9yLWljb24vcm91dGVcIjtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgc2VydmVySG9va3MsXG4gICAgICAgIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgb3JpZ2luYWxQYXRobmFtZSwgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-url-for-icon%2Froute&page=%2Fapi%2Fanalyze-url-for-icon%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-url-for-icon%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/analyze-url-for-icon/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/analyze-url-for-icon/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _utils_websiteAnalyzer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/websiteAnalyzer */ \"(rsc)/./src/utils/websiteAnalyzer.ts\");\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { url } = body;\n        if (!url || url.trim() === \"\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Please provide a valid URL.\"\n            }, {\n                status: 400\n            });\n        }\n        // Normalize URL\n        let normalizedUrl = url.trim();\n        if (!normalizedUrl.startsWith(\"http://\") && !normalizedUrl.startsWith(\"https://\")) {\n            normalizedUrl = \"https://\" + normalizedUrl;\n        }\n        // Get API key and model from environment variables\n        const apiKey = process.env.GOOGLE_AI_API_KEY;\n        const geminiModel = process.env.GEMINI_MODEL || \"gemini-2.5-flash-preview-05-20\";\n        if (!apiKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"API key not configured. Please set GOOGLE_AI_API_KEY environment variable.\"\n            }, {\n                status: 500\n            });\n        }\n        // Fetch the website content\n        let websiteContent = \"\";\n        let extractedColors = [];\n        let websiteInfo = {};\n        try {\n            const response = await fetch(normalizedUrl, {\n                headers: {\n                    \"User-Agent\": \"Mozilla/5.0 (compatible; IconGenerator/1.0; +https://example.com/bot)\"\n                },\n                signal: AbortSignal.timeout(10000)\n            });\n            if (response.ok) {\n                const html = await response.text();\n                // Extract basic info for display\n                const titleMatch = html.match(/<title[^>]*>([^<]+)<\\/title>/i);\n                const descMatch = html.match(/<meta[^>]*name=\"description\"[^>]*content=\"([^\"]*)\"[^>]*>/i);\n                const ogTitleMatch = html.match(/<meta[^>]*property=\"og:title\"[^>]*content=\"([^\"]*)\"[^>]*>/i);\n                const ogSiteMatch = html.match(/<meta[^>]*property=\"og:site_name\"[^>]*content=\"([^\"]*)\"[^>]*>/i);\n                // Extract colors from the HTML and external CSS\n                extractedColors = await (0,_utils_websiteAnalyzer__WEBPACK_IMPORTED_MODULE_1__.extractColorsWithCSS)(html, normalizedUrl);\n                console.log(\"\\uD83C\\uDFA8 Color extraction results for\", normalizedUrl);\n                console.log(\"\\uD83D\\uDCCA Number of colors found:\", extractedColors.length);\n                console.log(\"\\uD83C\\uDF08 Extracted colors:\", extractedColors);\n                websiteInfo = {\n                    title: titleMatch?.[1] || ogTitleMatch?.[1] || \"\",\n                    description: descMatch?.[1] || \"\",\n                    siteName: ogSiteMatch?.[1] || \"\",\n                    extractedColors\n                };\n                // Clean HTML for AI analysis\n                websiteContent = html.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, \"\").replace(/<style\\b[^<]*(?:(?!<\\/style>)<[^<]*)*<\\/style>/gi, \"\").replace(/<[^>]*>/g, \" \").replace(/\\s+/g, \" \").trim().slice(0, 10000); // Limit content for API efficiency\n            }\n        } catch (fetchError) {\n            console.warn(\"Could not fetch website content:\", fetchError);\n        // Continue with URL-only analysis\n        }\n        // Create AI prompt for icon generation\n        console.log(\"\\uD83E\\uDD16 Preparing AI prompt with colors:\", extractedColors);\n        const analysisPrompt = `Analyze this website and create a detailed prompt for generating a professional app icon.\n\nWebsite URL: ${normalizedUrl}\n${websiteInfo.title ? `Website Title: ${websiteInfo.title}` : \"\"}\n${websiteInfo.description ? `Website Description: ${websiteInfo.description}` : \"\"}\n${websiteInfo.siteName ? `Site Name: ${websiteInfo.siteName}` : \"\"}\n${extractedColors.length > 0 ? `Website Brand Colors: ${extractedColors.join(\", \")}` : \"No brand colors detected\"}\n\n${websiteContent ? `Website Content (first 10,000 characters):\\n${websiteContent}` : \"Website content could not be accessed - please analyze based on URL and any available metadata.\"}\n\nBased on your analysis of this website, create a comprehensive prompt for an AI image generator to create a professional app icon. The prompt MUST enforce STRICT COLOR COMPLIANCE and include:\n\n1. The website's purpose and industry\n2. Appropriate visual elements and symbols\n3. **MANDATORY COLOR COMPLIANCE**:\n   ${extractedColors.length > 0 ? `- CRITICAL: The icon MUST use ONLY these exact hex colors: ${extractedColors.join(\", \")}\n   - PRIMARY COLOR: ${extractedColors[0]} must be the dominant color in the icon\n   - SECONDARY COLORS: ${extractedColors.length > 1 ? extractedColors.slice(1).join(\", \") + \" must be used as accent colors\" : \"Use white or the primary color variations only\"}\n   - FORBIDDEN: Any colors not in this exact list: ${extractedColors.join(\", \")}\n   - NO VARIATIONS: Do not use lighter, darker, or modified versions of these colors\n   - EXACT MATCH REQUIRED: Use these hex values precisely as specified` : \"Choose appropriate colors but specify exact hex values and enforce their strict usage\"}\n4. Design style (modern, minimalist, corporate, etc.)\n5. Technical requirements for app store submission\n\n🔴 CRITICAL COLOR ENFORCEMENT INSTRUCTIONS:\n- Your prompt MUST include phrases like \"MUST USE ONLY\", \"FORBIDDEN COLORS\", \"EXACT HEX VALUES REQUIRED\"\n- Include negative prompts that explicitly forbid other colors\n- Add color validation language that treats the hex values as non-negotiable requirements\n- Structure the color requirements at the beginning of your prompt for maximum emphasis\n\n${extractedColors.length > 0 ? `EXAMPLE COLOR ENFORCEMENT LANGUAGE TO INCLUDE:\n\"🎨 MANDATORY COLOR REQUIREMENTS: This icon MUST use ONLY these exact hex colors: ${extractedColors.join(\", \")}. PRIMARY COLOR: ${extractedColors[0]} must dominate the design. FORBIDDEN: Do NOT use any other colors. NEGATIVE PROMPT: Avoid all colors except ${extractedColors.join(\", \")}. COLOR VALIDATION: Every pixel must match one of these exact hex values.\"` : \"Include similar strict color enforcement language with the colors you choose.\"}\n\nCreate a detailed, specific prompt that would generate an icon perfectly representing this website with ABSOLUTE color compliance. Your prompt should be structured to prioritize color requirements and include strong enforcement language.\n\nRespond with ONLY the icon generation prompt, no additional text or explanation.`;\n        const chatHistory = [\n            {\n                role: \"user\",\n                parts: [\n                    {\n                        text: analysisPrompt\n                    }\n                ]\n            }\n        ];\n        const payload = {\n            contents: chatHistory\n        };\n        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${geminiModel}:generateContent?key=${apiKey}`;\n        const response = await fetch(apiUrl, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(payload)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error?.message || \"AI analysis failed.\");\n        }\n        const result = await response.json();\n        if (result.candidates && result.candidates.length > 0 && result.candidates[0].content && result.candidates[0].content.parts && result.candidates[0].content.parts.length > 0) {\n            const generatedPrompt = result.candidates[0].content.parts[0].text.trim();\n            console.log(\"✅ AI generated prompt:\");\n            console.log(generatedPrompt);\n            console.log(\"\\uD83C\\uDFA8 Colors in final response:\", extractedColors);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                prompt: generatedPrompt,\n                websiteInfo\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Could not generate icon prompt from website analysis.\"\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error(\"Error analyzing URL for icon:\", error);\n        let errorMessage = \"Failed to analyze website\";\n        if (error instanceof Error) {\n            if (error.message.includes(\"fetch\")) {\n                errorMessage = \"Unable to access the website. Please check the URL and try again.\";\n            } else if (error.message.includes(\"timeout\")) {\n                errorMessage = \"Website took too long to respond. Please try again.\";\n            } else {\n                errorMessage = `Analysis failed: ${error.message}`;\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: errorMessage\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/analyze-url-for-icon/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/websiteAnalyzer.ts":
/*!**************************************!*\
  !*** ./src/utils/websiteAnalyzer.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractColors: () => (/* binding */ extractColors),\n/* harmony export */   extractColorsWithCSS: () => (/* binding */ extractColorsWithCSS),\n/* harmony export */   extractMetadata: () => (/* binding */ extractMetadata),\n/* harmony export */   scrapeWebsite: () => (/* binding */ scrapeWebsite),\n/* harmony export */   validateUrl: () => (/* binding */ validateUrl)\n/* harmony export */ });\n/* harmony import */ var cheerio__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cheerio */ \"(rsc)/./node_modules/cheerio/dist/esm/index.js\");\n\n/**\n * Validates if a URL is properly formatted and accessible\n */ function validateUrl(url) {\n    try {\n        // Add protocol if missing\n        let normalizedUrl = url.trim();\n        if (!normalizedUrl.startsWith(\"http://\") && !normalizedUrl.startsWith(\"https://\")) {\n            normalizedUrl = \"https://\" + normalizedUrl;\n        }\n        const urlObj = new URL(normalizedUrl);\n        // Basic validation\n        if (!urlObj.hostname || urlObj.hostname.length < 3) {\n            return {\n                isValid: false,\n                error: \"Invalid hostname\"\n            };\n        }\n        return {\n            isValid: true,\n            normalizedUrl\n        };\n    } catch (error) {\n        return {\n            isValid: false,\n            error: \"Invalid URL format\"\n        };\n    }\n}\n/**\n * Extracts metadata from HTML content using Cheerio\n */ function extractMetadata(html, url) {\n    const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(html);\n    // Extract basic metadata\n    const title = $(\"title\").text().trim() || $('meta[property=\"og:title\"]').attr(\"content\") || $('meta[name=\"twitter:title\"]').attr(\"content\") || \"\";\n    const description = $('meta[name=\"description\"]').attr(\"content\") || $('meta[property=\"og:description\"]').attr(\"content\") || $('meta[name=\"twitter:description\"]').attr(\"content\") || \"\";\n    const siteName = $('meta[property=\"og:site_name\"]').attr(\"content\") || \"\";\n    const keywords = $('meta[name=\"keywords\"]').attr(\"content\")?.split(\",\").map((k)=>k.trim()) || [];\n    const favicon = $('link[rel=\"icon\"]').attr(\"href\") || $('link[rel=\"shortcut icon\"]').attr(\"href\") || $('link[rel=\"apple-touch-icon\"]').attr(\"href\") || \"\";\n    const ogImage = $('meta[property=\"og:image\"]').attr(\"content\") || $('meta[name=\"twitter:image\"]').attr(\"content\") || \"\";\n    return {\n        url,\n        title,\n        description,\n        siteName,\n        keywords,\n        favicon: favicon ? new URL(favicon, url).href : undefined,\n        ogImage: ogImage ? new URL(ogImage, url).href : undefined\n    };\n}\n/**\n * Performs AI-powered analysis of website content using Gemini directly\n */ async function performAIAnalysis(html, metadata) {\n    try {\n        // Get API key and model from environment variables\n        const apiKey = process.env.GOOGLE_AI_API_KEY;\n        const geminiModel = process.env.GEMINI_MODEL || \"gemini-2.5-flash-preview-05-20\";\n        if (!apiKey) {\n            console.warn(\"Google AI API key not configured, skipping AI analysis\");\n            return undefined;\n        }\n        // Extract text content from HTML (simplified)\n        const textContent = html.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, \"\").replace(/<style\\b[^<]*(?:(?!<\\/style>)<[^<]*)*<\\/style>/gi, \"\").replace(/<[^>]*>/g, \" \").replace(/\\s+/g, \" \").trim().slice(0, 8000); // Limit text for API efficiency\n        const analysisPrompt = `Analyze this website and provide a structured analysis for icon generation purposes.\n\nWebsite Information:\n- URL: ${metadata.url || \"Not provided\"}\n- Title: ${metadata.title || \"Not provided\"}\n- Description: ${metadata.description || \"Not provided\"}\n- Site Name: ${metadata.siteName || \"Not provided\"}\n- Keywords: ${metadata.keywords?.join(\", \") || \"Not provided\"}\n\nWebsite Content (first 8000 characters):\n${textContent}\n\nPlease analyze this website and respond with a JSON object containing the following fields:\n\n{\n  \"category\": \"one of: ecommerce, blog, portfolio, saas, corporate, creative, educational, other\",\n  \"visualStyle\": \"one of: modern, minimalist, corporate, playful, elegant, bold, classic\",\n  \"brandPersonality\": \"brief description of the brand's personality and tone\",\n  \"targetAudience\": \"description of the primary target audience\",\n  \"primaryPurpose\": \"main purpose or function of the website\",\n  \"keyFeatures\": [\"array\", \"of\", \"key\", \"features\", \"or\", \"services\"],\n  \"industryContext\": \"industry or sector this website operates in\",\n  \"designCharacteristics\": [\"array\", \"of\", \"visual\", \"design\", \"characteristics\"]\n}\n\nBase your analysis on:\n1. The actual content and purpose of the website\n2. The language, tone, and messaging used\n3. The apparent target audience and use cases\n4. The industry context and competitive landscape\n5. Visual and functional characteristics that would inform icon design\n\nRespond ONLY with the JSON object, no additional text.`;\n        const chatHistory = [\n            {\n                role: \"user\",\n                parts: [\n                    {\n                        text: analysisPrompt\n                    }\n                ]\n            }\n        ];\n        const payload = {\n            contents: chatHistory\n        };\n        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${geminiModel}:generateContent?key=${apiKey}`;\n        const response = await fetch(apiUrl, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(payload)\n        });\n        if (!response.ok) {\n            console.warn(\"Gemini API request failed:\", response.status, response.statusText);\n            return undefined;\n        }\n        const result = await response.json();\n        if (result.candidates && result.candidates.length > 0 && result.candidates[0].content && result.candidates[0].content.parts && result.candidates[0].content.parts.length > 0) {\n            let analysisText = result.candidates[0].content.parts[0].text.trim();\n            // Clean up the response to extract JSON\n            analysisText = analysisText.replace(/```json\\s*/, \"\").replace(/```\\s*$/, \"\").trim();\n            try {\n                const analysis = JSON.parse(analysisText);\n                // Validate the response structure\n                if (!analysis.category || !analysis.visualStyle) {\n                    console.warn(\"Invalid AI analysis structure received\");\n                    return undefined;\n                }\n                return analysis;\n            } catch (parseError) {\n                console.warn(\"Failed to parse AI analysis JSON:\", parseError);\n                return undefined;\n            }\n        } else {\n            console.warn(\"No valid response from Gemini API\");\n            return undefined;\n        }\n    } catch (error) {\n        console.warn(\"AI analysis error:\", error);\n        return undefined;\n    }\n}\n/**\n * Basic fallback analysis when AI analysis fails\n */ function basicFallbackAnalysis(html, metadata) {\n    const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(html);\n    const text = $(\"body\").text().toLowerCase();\n    const title = metadata.title?.toLowerCase() || \"\";\n    const description = metadata.description?.toLowerCase() || \"\";\n    const allText = `${title} ${description} ${text}`.toLowerCase();\n    // Basic category determination\n    let category = \"other\";\n    if (allText.includes(\"shop\") || allText.includes(\"buy\") || allText.includes(\"cart\")) {\n        category = \"ecommerce\";\n    } else if (allText.includes(\"blog\") || allText.includes(\"article\")) {\n        category = \"blog\";\n    } else if (allText.includes(\"portfolio\") || allText.includes(\"work\")) {\n        category = \"portfolio\";\n    } else if (allText.includes(\"saas\") || allText.includes(\"software\")) {\n        category = \"saas\";\n    } else if (allText.includes(\"company\") || allText.includes(\"business\")) {\n        category = \"corporate\";\n    }\n    // Basic style determination\n    let visualStyle = \"modern\";\n    if (allText.includes(\"minimal\") || allText.includes(\"clean\")) {\n        visualStyle = \"minimalist\";\n    } else if (allText.includes(\"corporate\") || allText.includes(\"professional\")) {\n        visualStyle = \"corporate\";\n    }\n    return {\n        category,\n        visualStyle\n    };\n}\n/**\n * Extracts primary colors from CSS with improved detection\n */ function extractColors(html) {\n    console.log(\"\\uD83D\\uDD0D Starting color extraction from HTML (length:\", html.length, \"chars)\");\n    const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(html);\n    const colorCounts = {};\n    // Color regex for better matching\n    const colorRegex = /(#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}|rgb\\(\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*\\d+\\s*\\)|rgba\\(\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*[\\d.]+\\s*\\))/gi;\n    // Extract colors from inline styles with priority weighting\n    let inlineStyleCount = 0;\n    $(\"*\").each((_, element)=>{\n        const style = $(element).attr(\"style\");\n        if (style) {\n            inlineStyleCount++;\n            const colorMatches = style.match(colorRegex);\n            if (colorMatches) {\n                console.log(\"\\uD83C\\uDFA8 Found colors in inline style:\", colorMatches, \"from element:\", $(element).prop(\"tagName\"));\n                colorMatches.forEach((color)=>{\n                    const normalized = normalizeColor(color);\n                    if (normalized && !isCommonColor(normalized)) {\n                        // Give higher weight to background colors and brand elements\n                        const weight = style.includes(\"background\") ? 3 : style.includes(\"color\") ? 2 : 1;\n                        colorCounts[normalized] = (colorCounts[normalized] || 0) + weight;\n                        console.log(\"✅ Added color:\", normalized, \"with weight:\", weight);\n                    } else {\n                        console.log(\"❌ Skipped color:\", color, \"(normalized:\", normalized, \", common:\", isCommonColor(normalized || \"\"), \")\");\n                    }\n                });\n            }\n        }\n    });\n    console.log(\"\\uD83D\\uDCCA Processed\", inlineStyleCount, \"elements with inline styles\");\n    // Check for brand-related elements (headers, logos, buttons) in a separate loop\n    $(\"*\").each((_, element)=>{\n        const tagName = $(element).prop(\"tagName\")?.toLowerCase();\n        const className = $(element).attr(\"class\") || \"\";\n        const id = $(element).attr(\"id\") || \"\";\n        if (tagName === \"header\" || tagName === \"nav\" || className.includes(\"logo\") || className.includes(\"brand\") || className.includes(\"primary\") || className.includes(\"accent\") || id.includes(\"logo\") || id.includes(\"brand\")) {\n            const computedStyle = $(element).attr(\"style\");\n            if (computedStyle) {\n                const brandColors = computedStyle.match(colorRegex);\n                if (brandColors) {\n                    console.log(\"\\uD83C\\uDFF7️ Found brand colors in\", tagName, \"element:\", brandColors);\n                    brandColors.forEach((color)=>{\n                        const normalized = normalizeColor(color);\n                        if (normalized && !isCommonColor(normalized)) {\n                            colorCounts[normalized] = (colorCounts[normalized] || 0) + 5; // High priority for brand colors\n                            console.log(\"⭐ Added brand color:\", normalized);\n                        }\n                    });\n                }\n            }\n        }\n    });\n    // Extract from style tags and CSS\n    let styleTagCount = 0;\n    $(\"style\").each((_, element)=>{\n        styleTagCount++;\n        const css = $(element).html() || \"\";\n        console.log(\"\\uD83C\\uDFA8 Processing style tag\", styleTagCount, \"(length:\", css.length, \"chars)\");\n        const colorMatches = css.match(colorRegex);\n        if (colorMatches) {\n            console.log(\"\\uD83C\\uDFA8 Found colors in CSS:\", colorMatches);\n            colorMatches.forEach((color)=>{\n                const normalized = normalizeColor(color);\n                if (normalized && !isCommonColor(normalized)) {\n                    // Check if it's in important CSS rules\n                    const weight = css.includes(\":root\") || css.includes(\"--\") ? 4 : css.includes(\".primary\") || css.includes(\".brand\") ? 3 : css.includes(\"background\") ? 2 : 1;\n                    colorCounts[normalized] = (colorCounts[normalized] || 0) + weight;\n                    console.log(\"✅ Added CSS color:\", normalized, \"with weight:\", weight);\n                }\n            });\n        } else {\n            console.log(\"❌ No colors found in this style tag\");\n        }\n    });\n    console.log(\"\\uD83D\\uDCCA Processed\", styleTagCount, \"style tags\");\n    // Also try to extract from any CSS custom properties in the HTML\n    const htmlContent = $.html();\n    const cssVarMatches = htmlContent.match(/--[\\w-]+:\\s*(#[0-9a-fA-F]{3,6}|rgb\\([^)]+\\)|rgba\\([^)]+\\))/gi);\n    if (cssVarMatches) {\n        console.log(\"\\uD83C\\uDFA8 Found CSS custom properties:\", cssVarMatches);\n        cssVarMatches.forEach((match)=>{\n            const colorMatch = match.match(colorRegex);\n            if (colorMatch) {\n                colorMatch.forEach((color)=>{\n                    const normalized = normalizeColor(color);\n                    if (normalized && !isCommonColor(normalized)) {\n                        colorCounts[normalized] = (colorCounts[normalized] || 0) + 4; // High priority for CSS vars\n                        console.log(\"⭐ Added CSS variable color:\", normalized);\n                    }\n                });\n            }\n        });\n    }\n    // If we haven't found many colors, try some fallback methods\n    if (Object.keys(colorCounts).length < 3) {\n        console.log(\"\\uD83D\\uDD0D Low color count, trying fallback methods...\");\n        // Look for data attributes that might contain colors\n        $(\"*\").each((_, element)=>{\n            const $el = $(element);\n            // Check all attributes for color values\n            const attributes = $el.get(0)?.attributes;\n            if (attributes) {\n                for(let i = 0; i < attributes.length; i++){\n                    const attr = attributes[i];\n                    if (attr.name.includes(\"color\") || attr.name.includes(\"theme\") || attr.name.startsWith(\"data-\")) {\n                        const colorMatch = attr.value.match(colorRegex);\n                        if (colorMatch) {\n                            console.log(\"\\uD83C\\uDFA8 Found color in attribute\", attr.name, \":\", colorMatch);\n                            colorMatch.forEach((color)=>{\n                                const normalized = normalizeColor(color);\n                                if (normalized && !isCommonColor(normalized)) {\n                                    colorCounts[normalized] = (colorCounts[normalized] || 0) + 2;\n                                    console.log(\"✅ Added fallback color:\", normalized);\n                                }\n                            });\n                        }\n                    }\n                }\n            }\n        });\n        // Look for common color class patterns\n        const colorClassPatterns = [\n            \"bg-blue\",\n            \"bg-red\",\n            \"bg-green\",\n            \"bg-purple\",\n            \"bg-yellow\",\n            \"bg-pink\",\n            \"bg-indigo\",\n            \"text-blue\",\n            \"text-red\",\n            \"text-green\",\n            \"text-purple\",\n            \"text-yellow\",\n            \"text-pink\",\n            \"primary\",\n            \"secondary\",\n            \"accent\",\n            \"brand\"\n        ];\n        colorClassPatterns.forEach((pattern)=>{\n            const elements = $(`.${pattern}, [class*=\"${pattern}\"]`);\n            if (elements.length > 0) {\n                console.log(\"\\uD83C\\uDFA8 Found elements with color class pattern:\", pattern, \"(\", elements.length, \"elements)\");\n                // Add some default colors for common patterns (this is a fallback)\n                if (pattern.includes(\"blue\")) colorCounts[\"#3b82f6\"] = (colorCounts[\"#3b82f6\"] || 0) + 1;\n                if (pattern.includes(\"red\")) colorCounts[\"#ef4444\"] = (colorCounts[\"#ef4444\"] || 0) + 1;\n                if (pattern.includes(\"green\")) colorCounts[\"#10b981\"] = (colorCounts[\"#10b981\"] || 0) + 1;\n                if (pattern.includes(\"purple\")) colorCounts[\"#8b5cf6\"] = (colorCounts[\"#8b5cf6\"] || 0) + 1;\n            }\n        });\n    }\n    // Sort by frequency/importance and return top colors\n    const sortedColors = Object.entries(colorCounts).sort(([, a], [, b])=>b - a).map(([color])=>color).slice(0, 5);\n    console.log(\"\\uD83D\\uDCC8 Color extraction summary:\");\n    console.log(\"- Total unique colors found:\", Object.keys(colorCounts).length);\n    console.log(\"- Color frequency map:\", colorCounts);\n    console.log(\"- Top 5 colors returned:\", sortedColors);\n    return sortedColors;\n}\n/**\n * Enhanced color extraction that also fetches external CSS files\n */ async function extractColorsWithCSS(html, baseUrl) {\n    console.log(\"\\uD83D\\uDD0D Starting enhanced color extraction with CSS files\");\n    // First, extract colors from the HTML itself\n    const htmlColors = extractColors(html);\n    console.log(\"\\uD83D\\uDCC4 Colors from HTML:\", htmlColors);\n    // Parse the HTML to find CSS file links\n    const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(html);\n    const cssUrls = [];\n    // Find all CSS links\n    $('link[rel=\"stylesheet\"]').each((_, element)=>{\n        const href = $(element).attr(\"href\");\n        if (href) {\n            try {\n                // Convert relative URLs to absolute\n                const cssUrl = href.startsWith(\"http\") ? href : new URL(href, baseUrl).href;\n                cssUrls.push(cssUrl);\n                console.log(\"\\uD83D\\uDD17 Found CSS file:\", cssUrl);\n            } catch (error) {\n                console.warn(\"❌ Invalid CSS URL:\", href);\n            }\n        }\n    });\n    console.log(\"\\uD83D\\uDCCB Total CSS files to fetch:\", cssUrls.length);\n    // Fetch and analyze each CSS file\n    const cssColors = [];\n    const colorRegex = /(#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}|rgb\\(\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*\\d+\\s*\\)|rgba\\(\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*[\\d.]+\\s*\\))/gi;\n    for (const cssUrl of cssUrls.slice(0, 5)){\n        try {\n            console.log(\"\\uD83D\\uDCE5 Fetching CSS file:\", cssUrl);\n            const cssResponse = await fetch(cssUrl, {\n                headers: {\n                    \"User-Agent\": \"Mozilla/5.0 (compatible; IconGenerator/1.0; +https://example.com/bot)\"\n                },\n                signal: AbortSignal.timeout(5000)\n            });\n            if (cssResponse.ok) {\n                const cssContent = await cssResponse.text();\n                console.log(\"\\uD83D\\uDCC4 CSS file size:\", cssContent.length, \"characters\");\n                const colorMatches = cssContent.match(colorRegex);\n                if (colorMatches) {\n                    console.log(\"\\uD83C\\uDFA8 Found colors in CSS file:\", colorMatches.slice(0, 10), \"(showing first 10)\");\n                    // Process and normalize colors\n                    const colorCounts = {};\n                    colorMatches.forEach((color)=>{\n                        const normalized = normalizeColor(color);\n                        if (normalized && !isCommonColor(normalized)) {\n                            colorCounts[normalized] = (colorCounts[normalized] || 0) + 1;\n                        }\n                    });\n                    // Get top colors from this CSS file\n                    const topColors = Object.entries(colorCounts).sort(([, a], [, b])=>b - a).map(([color])=>color).slice(0, 10);\n                    cssColors.push(...topColors);\n                    console.log(\"✅ Added colors from CSS:\", topColors);\n                } else {\n                    console.log(\"❌ No colors found in CSS file\");\n                }\n            } else {\n                console.warn(\"❌ Failed to fetch CSS file:\", cssResponse.status, cssResponse.statusText);\n            }\n        } catch (error) {\n            console.warn(\"❌ Error fetching CSS file:\", cssUrl, error);\n        }\n    }\n    // Combine HTML and CSS colors, remove duplicates, and prioritize\n    const allColors = [\n        ...htmlColors,\n        ...cssColors\n    ];\n    const uniqueColors = [\n        ...new Set(allColors)\n    ];\n    console.log(\"\\uD83C\\uDFA8 Final color extraction summary:\");\n    console.log(\"- HTML colors:\", htmlColors.length);\n    console.log(\"- CSS colors:\", cssColors.length);\n    console.log(\"- Total unique colors:\", uniqueColors.length);\n    console.log(\"- Final colors:\", uniqueColors.slice(0, 5));\n    return uniqueColors.slice(0, 5); // Return top 5 colors\n}\n/**\n * Normalizes color format to hex\n */ function normalizeColor(color) {\n    const trimmed = color.trim().toLowerCase();\n    // Already hex\n    if (trimmed.match(/^#[0-9a-f]{6}$/)) {\n        return trimmed;\n    }\n    // 3-digit hex\n    if (trimmed.match(/^#[0-9a-f]{3}$/)) {\n        return `#${trimmed[1]}${trimmed[1]}${trimmed[2]}${trimmed[2]}${trimmed[3]}${trimmed[3]}`;\n    }\n    // RGB\n    const rgbMatch = trimmed.match(/rgb\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*\\)/);\n    if (rgbMatch) {\n        const r = parseInt(rgbMatch[1]).toString(16).padStart(2, \"0\");\n        const g = parseInt(rgbMatch[2]).toString(16).padStart(2, \"0\");\n        const b = parseInt(rgbMatch[3]).toString(16).padStart(2, \"0\");\n        return `#${r}${g}${b}`;\n    }\n    // RGBA (ignore alpha for now)\n    const rgbaMatch = trimmed.match(/rgba\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*[\\d.]+\\s*\\)/);\n    if (rgbaMatch) {\n        const r = parseInt(rgbaMatch[1]).toString(16).padStart(2, \"0\");\n        const g = parseInt(rgbaMatch[2]).toString(16).padStart(2, \"0\");\n        const b = parseInt(rgbaMatch[3]).toString(16).padStart(2, \"0\");\n        return `#${r}${g}${b}`;\n    }\n    return null;\n}\n/**\n * Checks if a color is too common/generic to be useful\n */ function isCommonColor(color) {\n    const commonColors = [\n        \"#000000\",\n        \"#ffffff\",\n        \"#000\",\n        \"#fff\",\n        \"#f0f0f0\",\n        \"#e0e0e0\",\n        \"#d0d0d0\",\n        \"#c0c0c0\",\n        \"#808080\",\n        \"#404040\",\n        \"#202020\",\n        \"#f8f8f8\",\n        \"#f5f5f5\",\n        \"#eeeeee\",\n        \"#dddddd\"\n    ];\n    return commonColors.includes(color.toLowerCase());\n}\n/**\n * Main function to scrape and analyze a website\n */ async function scrapeWebsite(url) {\n    const validation = validateUrl(url);\n    if (!validation.isValid) {\n        throw new Error(validation.error || \"Invalid URL\");\n    }\n    const normalizedUrl = validation.normalizedUrl;\n    try {\n        // Fetch the website content\n        const response = await fetch(normalizedUrl, {\n            headers: {\n                \"User-Agent\": \"Mozilla/5.0 (compatible; IconGenerator/1.0; +https://example.com/bot)\"\n            },\n            signal: AbortSignal.timeout(10000)\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const html = await response.text();\n        // Extract metadata\n        const metadata = extractMetadata(html, normalizedUrl);\n        // Perform AI-powered analysis\n        const aiAnalysis = await performAIAnalysis(html, metadata);\n        // Extract colors\n        const primaryColors = extractColors(html);\n        // Use AI analysis if available, otherwise fall back to basic analysis\n        let category = \"other\";\n        let visualStyle = \"modern\";\n        if (aiAnalysis) {\n            category = aiAnalysis.category;\n            visualStyle = aiAnalysis.visualStyle;\n        } else {\n            // Fallback to basic analysis\n            const fallback = basicFallbackAnalysis(html, metadata);\n            category = fallback.category;\n            visualStyle = fallback.visualStyle;\n        }\n        return {\n            url: normalizedUrl,\n            title: metadata.title,\n            description: metadata.description,\n            siteName: metadata.siteName,\n            keywords: metadata.keywords,\n            primaryColors,\n            category,\n            visualStyle,\n            favicon: metadata.favicon,\n            ogImage: metadata.ogImage,\n            aiAnalysis\n        };\n    } catch (error) {\n        if (error instanceof Error) {\n            throw new Error(`Failed to scrape website: ${error.message}`);\n        }\n        throw new Error(\"Failed to scrape website: Unknown error\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/websiteAnalyzer.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/undici","vendor-chunks/iconv-lite","vendor-chunks/parse5","vendor-chunks/cheerio","vendor-chunks/css-select","vendor-chunks/entities","vendor-chunks/domutils","vendor-chunks/htmlparser2","vendor-chunks/whatwg-mimetype","vendor-chunks/nth-check","vendor-chunks/cheerio-select","vendor-chunks/whatwg-encoding","vendor-chunks/encoding-sniffer","vendor-chunks/domhandler","vendor-chunks/dom-serializer","vendor-chunks/css-what","vendor-chunks/parse5-parser-stream","vendor-chunks/parse5-htmlparser2-tree-adapter","vendor-chunks/domelementtype","vendor-chunks/safer-buffer","vendor-chunks/boolbase"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalyze-url-for-icon%2Froute&page=%2Fapi%2Fanalyze-url-for-icon%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-url-for-icon%2Froute.ts&appDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpienaaranker%2Fsrc%2Fpersonal%2Ficon-generator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();